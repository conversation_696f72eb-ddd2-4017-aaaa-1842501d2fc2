import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/screens/ar_explore_screen.dart';

/// AR Guide Screen - Main hub for AR experiences
/// Redesigned to match React Native reference implementation with pixel-perfect accuracy
class ARGuideScreen extends ConsumerWidget {
  const ARGuideScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingMd,
            vertical: AppTheme.spacingSm,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Modern Header Section
              _buildModernHeader(context),

              const SizedBox(height: AppTheme.spacingXl),

              // Enhanced Quick AR Actions
              _buildEnhancedQuickActions(context),

              const SizedBox(height: AppTheme.spacingXl),

              // Redesigned Featured AR Experiences
              _buildFeaturedARExperiences(context),

              const SizedBox(height: AppTheme.spacingXl),

              // Modern AR Tutorial Section
              _buildModernTutorialSection(context),

              // Bottom spacing for better scrolling
              const SizedBox(height: AppTheme.spacingLg),
            ],
          ),
        ),
      ),
    );
  }

  /// Modern header section with enhanced styling
  Widget _buildModernHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: AppTheme.spacingMd),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main title with modern typography
          const Text(
            'AR Guide',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.w700,
              color: AppTheme.textPrimaryColor,
              fontFamily: AppTheme.fontFamily,
              letterSpacing: -0.5,
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),

          // Subtitle with enhanced styling
          const Text(
            'Explore the world through augmented reality',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: AppTheme.textSecondaryColor,
              fontFamily: AppTheme.fontFamily,
              height: 1.4,
            ),
          ),

          const SizedBox(height: AppTheme.spacingMd),

          // Modern accent line
          Container(
            width: 60,
            height: 4,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFF6366F1), // Primary indigo
                  Color(0xFF06B6D4), // Cyan accent
                ],
              ),
              borderRadius: BorderRadius.all(Radius.circular(2)),
            ),
          ),
        ],
      ),
    );
  }

  /// Enhanced quick actions section with modern design
  Widget _buildEnhancedQuickActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
        border: Border.all(
          color: AppTheme.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header with modern styling
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6366F1).withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.flash_on,
                  color: Color(0xFF6366F1),
                  size: 20,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              const Expanded(
                child: Text(
                  'Quick Actions',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                    fontFamily: AppTheme.fontFamily,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingLg),

          // Modern action buttons grid
          Row(
            children: [
              Expanded(
                child: _buildModernActionButton(
                  context,
                  'Start AR',
                  'Launch AR camera',
                  Icons.camera_alt_rounded,
                  const Color(0xFF6366F1),
                  () => _startARExperience(context),
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: _buildModernActionButton(
                  context,
                  'AR Tutorial',
                  'Learn AR basics',
                  Icons.school_rounded,
                  const Color(0xFF06B6D4),
                  () => _openTutorial(context),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMd),

          // Additional action buttons
          Row(
            children: [
              Expanded(
                child: _buildModernActionButton(
                  context,
                  'AR Gallery',
                  'View saved content',
                  Icons.photo_library_rounded,
                  const Color(0xFF10B981),
                  () => _openARGallery(context),
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              Expanded(
                child: _buildModernActionButton(
                  context,
                  'Settings',
                  'AR preferences',
                  Icons.settings_rounded,
                  const Color(0xFF8B5CF6),
                  () => _openARSettings(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Modern action button with enhanced styling and animations
  Widget _buildModernActionButton(
    BuildContext context,
    String label,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          color: color.withAlpha(26),
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          border: Border.all(
            color: color.withAlpha(51),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon with background
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),

            const SizedBox(height: AppTheme.spacingMd),

            // Label
            Text(
              label,
              style: const TextStyle(
                color: AppTheme.textPrimaryColor,
                fontWeight: FontWeight.w600,
                fontSize: 14,
                fontFamily: AppTheme.fontFamily,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 2),

            // Subtitle
            Text(
              subtitle,
              style: const TextStyle(
                color: AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w400,
                fontSize: 12,
                fontFamily: AppTheme.fontFamily,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// Redesigned featured AR experiences section with modern cards
  Widget _buildFeaturedARExperiences(BuildContext context) {
    final experiences = [
      {
        'title': 'Historic Landmarks',
        'subtitle': 'Explore ancient sites',
        'icon': Icons.account_balance_rounded,
        'color': const Color(0xFF6366F1),
        'badge': 'Popular',
      },
      {
        'title': 'Cultural Tours',
        'subtitle': 'Immersive experiences',
        'icon': Icons.museum_rounded,
        'color': const Color(0xFF06B6D4),
        'badge': 'New',
      },
      {
        'title': 'Nature Walks',
        'subtitle': 'Discover wildlife',
        'icon': Icons.nature_people_rounded,
        'color': const Color(0xFF10B981),
        'badge': 'Trending',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF6366F1).withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.stars_rounded,
                color: Color(0xFF6366F1),
                size: 20,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMd),
            const Expanded(
              child: Text(
                'Featured AR Experiences',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                  fontFamily: AppTheme.fontFamily,
                ),
              ),
            ),
            TextButton(
              onPressed: () => _viewAllExperiences(context),
              child: const Text(
                'View All',
                style: TextStyle(
                  color: Color(0xFF6366F1),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: AppTheme.spacingLg),

        // Modern experience cards
        SizedBox(
          height: 240,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.zero,
            itemCount: experiences.length,
            itemBuilder: (context, index) {
              final experience = experiences[index];
              return Container(
                width: 180,
                margin: EdgeInsets.only(
                  right:
                      index < experiences.length - 1 ? AppTheme.spacingMd : 0,
                ),
                child: _buildModernExperienceCard(
                  context,
                  experience['title'] as String,
                  experience['subtitle'] as String,
                  experience['icon'] as IconData,
                  experience['color'] as Color,
                  experience['badge'] as String,
                  index,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Modern experience card with enhanced styling
  Widget _buildModernExperienceCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    String badge,
    int index,
  ) {
    return GestureDetector(
      onTap: () => _openARExperience(context, index),
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          boxShadow: AppTheme.shadowMedium,
          border: Border.all(
            color: AppTheme.borderLight,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and badge
            Container(
              height: 140,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    color.withAlpha(51),
                    color.withAlpha(26),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppTheme.borderRadiusLarge),
                  topRight: Radius.circular(AppTheme.borderRadiusLarge),
                ),
              ),
              child: Stack(
                children: [
                  // Main icon
                  Center(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        icon,
                        size: 32,
                        color: Colors.white,
                      ),
                    ),
                  ),

                  // Badge
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        badge,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMd),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: AppTheme.textPrimaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const Spacer(),

                    // Action indicator
                    Row(
                      children: [
                        Icon(
                          Icons.play_circle_outline,
                          size: 16,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Start Experience',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Modern tutorial section with enhanced design
  Widget _buildModernTutorialSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF6366F1),
            Color(0xFF8B5CF6),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(51),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.school_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              const Expanded(
                child: Text(
                  'New to AR?',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMd),

          const Text(
            'Learn how to use AR features with our interactive tutorial and start exploring the world in a whole new way.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              height: 1.4,
            ),
          ),

          const SizedBox(height: AppTheme.spacingLg),

          // Modern button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _openTutorial(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF6366F1),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.play_arrow_rounded, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Start Tutorial',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Navigation and action methods
  void _startARExperience(BuildContext context) {
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const ARExploreScreen(),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to start AR experience');
    }
  }

  void _openTutorial(BuildContext context) {
    try {
      // Navigate to AR tutorial screen
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('AR Tutorial coming soon!'),
          backgroundColor: const Color(0xFF6366F1),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to open tutorial');
    }
  }

  void _openARExperience(BuildContext context, int index) {
    try {
      // Navigate to specific AR experience
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Opening AR Experience ${index + 1}...'),
          backgroundColor: const Color(0xFF6366F1),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to open AR experience');
    }
  }

  void _viewAllExperiences(BuildContext context) {
    try {
      // Navigate to all experiences screen
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Opening all experiences...'),
          backgroundColor: const Color(0xFF6366F1),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to view all experiences');
    }
  }

  void _openARGallery(BuildContext context) {
    try {
      // Navigate to AR gallery
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Opening AR Gallery...'),
          backgroundColor: const Color(0xFF10B981),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to open AR gallery');
    }
  }

  void _openARSettings(BuildContext context) {
    try {
      // Navigate to AR settings
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Opening AR Settings...'),
          backgroundColor: const Color(0xFF8B5CF6),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to open AR settings');
    }
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppTheme.errorColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }
}
