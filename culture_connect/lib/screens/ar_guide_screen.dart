import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/screens/ar_explore_screen.dart';

/// AR Guide Screen - Main hub for AR experiences
/// Redesigned to match React Native reference implementation with pixel-perfect accuracy
class ARGuideScreen extends ConsumerWidget {
  const ARGuideScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacingMd,
            vertical: AppTheme.spacingSm,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Modern Header Section
              _buildModernHeader(context),

              const SizedBox(height: AppTheme.spacingXl),

              // Enhanced Quick AR Actions
              _buildEnhancedQuickActions(context),

              const SizedBox(height: AppTheme.spacingXl),

              // Redesigned Featured AR Experiences
              _buildFeaturedARExperiences(context),

              const SizedBox(height: AppTheme.spacingXl),

              // Modern AR Tutorial Section
              _buildModernTutorialSection(context),

              // Bottom spacing for better scrolling
              const SizedBox(height: AppTheme.spacingLg),
            ],
          ),
        ),
      ),
    );
  }

  /// Pixel-perfect header section matching reference design
  Widget _buildModernHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(
        top: 8,
        bottom: 24,
        left: 4,
        right: 4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Navigation bar with back button and menu
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Back button
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(13),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.arrow_back_ios_new,
                  color: Color(0xFF1F2937),
                  size: 18,
                ),
              ),

              // Menu button
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(13),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.more_horiz,
                  color: Color(0xFF1F2937),
                  size: 20,
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Main title with exact reference styling
          const Text(
            'AR Guide',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.w700,
              color: Color(0xFF1F2937),
              fontFamily: AppTheme.fontFamily,
              letterSpacing: -0.3,
              height: 1.2,
            ),
          ),

          const SizedBox(height: 8),

          // Subtitle with reference styling
          const Text(
            'Discover places through augmented reality',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: Color(0xFF6B7280),
              fontFamily: AppTheme.fontFamily,
              height: 1.5,
            ),
          ),

          const SizedBox(height: 24),

          // Search bar matching reference design
          Container(
            height: 52,
            decoration: BoxDecoration(
              color: const Color(0xFFF9FAFB),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFFE5E7EB),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                const SizedBox(width: 16),
                const Icon(
                  Icons.search,
                  color: Color(0xFF9CA3AF),
                  size: 20,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Search AR experiences...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFF9CA3AF),
                      fontFamily: AppTheme.fontFamily,
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6366F1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Icon(
                    Icons.tune,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Quick actions section matching reference design
  Widget _buildEnhancedQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 4),
          child: Text(
            'Quick Start',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1F2937),
              fontFamily: AppTheme.fontFamily,
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Quick action cards
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                'Start AR Camera',
                'Begin AR experience',
                Icons.camera_alt_rounded,
                const Color(0xFF6366F1),
                () => _startARExperience(context),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                'Scan QR Code',
                'Quick AR access',
                Icons.qr_code_scanner,
                const Color(0xFF06B6D4),
                () => _openQRScanner(context),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                'AR Gallery',
                'View saved content',
                Icons.photo_library_rounded,
                const Color(0xFF10B981),
                () => _openARGallery(context),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                'Tutorial',
                'Learn AR basics',
                Icons.school_rounded,
                const Color(0xFF8B5CF6),
                () => _openTutorial(context),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Quick action card matching reference design
  Widget _buildQuickActionCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: const Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon container
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withAlpha(26),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),

            const SizedBox(height: 12),

            // Title
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1F2937),
                fontFamily: AppTheme.fontFamily,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 4),

            // Subtitle
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Color(0xFF6B7280),
                fontFamily: AppTheme.fontFamily,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// Redesigned featured AR experiences section with modern cards
  Widget _buildFeaturedARExperiences(BuildContext context) {
    final experiences = [
      {
        'title': 'Historic Landmarks',
        'subtitle': 'Explore ancient sites',
        'icon': Icons.account_balance_rounded,
        'color': const Color(0xFF6366F1),
        'badge': 'Popular',
      },
      {
        'title': 'Cultural Tours',
        'subtitle': 'Immersive experiences',
        'icon': Icons.museum_rounded,
        'color': const Color(0xFF06B6D4),
        'badge': 'New',
      },
      {
        'title': 'Nature Walks',
        'subtitle': 'Discover wildlife',
        'icon': Icons.nature_people_rounded,
        'color': const Color(0xFF10B981),
        'badge': 'Trending',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF6366F1).withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.stars_rounded,
                color: Color(0xFF6366F1),
                size: 20,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMd),
            const Expanded(
              child: Text(
                'Featured AR Experiences',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                  fontFamily: AppTheme.fontFamily,
                ),
              ),
            ),
            TextButton(
              onPressed: () => _viewAllExperiences(context),
              child: const Text(
                'View All',
                style: TextStyle(
                  color: Color(0xFF6366F1),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: AppTheme.spacingLg),

        // Modern experience cards
        SizedBox(
          height: 240,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.zero,
            itemCount: experiences.length,
            itemBuilder: (context, index) {
              final experience = experiences[index];
              return Container(
                width: 180,
                margin: EdgeInsets.only(
                  right:
                      index < experiences.length - 1 ? AppTheme.spacingMd : 0,
                ),
                child: _buildModernExperienceCard(
                  context,
                  experience['title'] as String,
                  experience['subtitle'] as String,
                  experience['icon'] as IconData,
                  experience['color'] as Color,
                  experience['badge'] as String,
                  index,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Modern experience card with enhanced styling
  Widget _buildModernExperienceCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    String badge,
    int index,
  ) {
    return GestureDetector(
      onTap: () => _openARExperience(context, index),
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          boxShadow: AppTheme.shadowMedium,
          border: Border.all(
            color: AppTheme.borderLight,
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon and badge
            Container(
              height: 140,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    color.withAlpha(51),
                    color.withAlpha(26),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(AppTheme.borderRadiusLarge),
                  topRight: Radius.circular(AppTheme.borderRadiusLarge),
                ),
              ),
              child: Stack(
                children: [
                  // Main icon
                  Center(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        icon,
                        size: 32,
                        color: Colors.white,
                      ),
                    ),
                  ),

                  // Badge
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        badge,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacingMd),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: AppTheme.textPrimaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const Spacer(),

                    // Action indicator
                    Row(
                      children: [
                        Icon(
                          Icons.play_circle_outline,
                          size: 16,
                          color: color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Start Experience',
                          style: TextStyle(
                            fontSize: 12,
                            color: color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Modern tutorial section with enhanced design
  Widget _buildModernTutorialSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingLg),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF6366F1),
            Color(0xFF8B5CF6),
          ],
        ),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        boxShadow: AppTheme.shadowMedium,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(51),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.school_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: AppTheme.spacingMd),
              const Expanded(
                child: Text(
                  'New to AR?',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacingMd),

          const Text(
            'Learn how to use AR features with our interactive tutorial and start exploring the world in a whole new way.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              height: 1.4,
            ),
          ),

          const SizedBox(height: AppTheme.spacingLg),

          // Modern button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _openTutorial(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF6366F1),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.play_arrow_rounded, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Start Tutorial',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Navigation and action methods
  void _startARExperience(BuildContext context) {
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const ARExploreScreen(),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to start AR experience');
    }
  }

  void _openTutorial(BuildContext context) {
    try {
      // Navigate to AR tutorial screen
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('AR Tutorial coming soon!'),
          backgroundColor: const Color(0xFF6366F1),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to open tutorial');
    }
  }

  void _openARExperience(BuildContext context, int index) {
    try {
      // Navigate to specific AR experience
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Opening AR Experience ${index + 1}...'),
          backgroundColor: const Color(0xFF6366F1),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to open AR experience');
    }
  }

  void _viewAllExperiences(BuildContext context) {
    try {
      // Navigate to all experiences screen
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Opening all experiences...'),
          backgroundColor: const Color(0xFF6366F1),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to view all experiences');
    }
  }

  void _openARGallery(BuildContext context) {
    try {
      // Navigate to AR gallery
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Opening AR Gallery...'),
          backgroundColor: const Color(0xFF10B981),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to open AR gallery');
    }
  }

  void _openQRScanner(BuildContext context) {
    try {
      // Navigate to QR scanner
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Opening QR Scanner...'),
          backgroundColor: const Color(0xFF06B6D4),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to open QR scanner');
    }
  }

  void _openARMap(BuildContext context) {
    try {
      // Navigate to AR map
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Opening AR Map...'),
          backgroundColor: const Color(0xFF6366F1),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      _showErrorSnackBar(context, 'Failed to open AR map');
    }
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppTheme.errorColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }
}
